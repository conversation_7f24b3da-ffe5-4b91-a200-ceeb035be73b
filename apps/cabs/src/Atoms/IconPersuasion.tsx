import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
// @ts-ignore
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
// @ts-ignore
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import EmptyCheckerText from '../Components/EmptyCheckerText';
import { seggregateContent } from '../utils/cabsCommonUtils';
import { SvgUri } from 'react-native-svg';

interface IconPersuasion {
  icon: string;
  title: string;
  content: string;
  height: number;
  isSvg: boolean;
}

const IconPersuasion = ({ icon, title, content, height, isSvg }: IconPersuasion) => (
  <View style={[styles.container, { minHeight: height }]}>
    {isSvg ? (
      <View style={styles.imageContainer}>
        <SvgUri width={34} height={34} uri={icon} />
      </View>
    ) : (
      <Image
        source={{
          uri: icon,
        }}
        style={styles.imageContainer}
      />
    )}
    <EmptyCheckerText style={styles.header}>{title}</EmptyCheckerText>
    <Text style={styles.description}>
      {seggregateContent(content, [styles.description, styles.highlightedText], styles.description)}
    </Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    borderRadius: 10,
    backgroundColor: colors.white,
    paddingHorizontal: 10,
    paddingVertical: 10,
    width: 185,
    ...getPlatformElevation(4),
  },
  header: {
    fontSize: 12,
    fontFamily: fonts.black,
    lineHeight: 14,
    color: colors.black,
    marginTop: 12,
  },
  highlightedText: {
    fontFamily: fonts.black,
  },
  description: {
    fontSize: 10,
    fontFamily: fonts.regular,
    lineHeight: 14,
    color: colors.black,
    marginTop: 3,
  },
  imageContainer: {
    height: 34,
    width: 34,
    borderRadius: 50,
    position: 'absolute',
    top: -17,
    left: 10,
  },
});

export default IconPersuasion;
