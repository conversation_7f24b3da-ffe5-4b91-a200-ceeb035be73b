import { Dimensions } from 'react-native';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import DynamicStyleSheet from 'apps/cabs/src/utils/theme/DynamicStyleSheet';

const deviceWidth = Dimensions.get('window').width;

const styles = DynamicStyleSheet.create((theme) => ({
  flexRow: { flexDirection: 'row', alignItems: 'center' },
  leftContainer: {},
  rightContainer: {},
  carTypeText: {
    color: colors.white,
    fontSize: 12,
    lineHeight: 14,
    fontFamily: fonts.regular,
  },
  carNameStyle: {
    fontSize: 16,
    lineHeight: 18,
    color: colors.black,
    fontFamily: fonts.bold,
    fontWeight: '900',
  },
  marqueeTextWrapper: {
    flexDirection: 'row',
  },
  safteyContainer: {
    position: 'absolute',
    top: -5,
    zIndex: 5,
    left: 23,
    flexDirection: 'row',
  },
  carouselStyle: {
    flex: 1,
    width: Dimensions.get('screen').width - 45,
  },
  cardContainer: {
    // marginHorizontal: 16,
    borderWidth: 1,
    backgroundColor: colors.grey22,
    borderColor: colors.lightGrey,
    borderRadius: 16,
    padding: 16,
    gap: 12,
  },
  cardWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    flex: 1,
    gap: 16,
  },
  dashedSeperator: {
    width: 71,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: colors.lightSilver,
    marginTop: 8,
    marginBottom: 4,
  },
  partnerIconStyle: {
    width: '85%',
    maxWidth: 50,
    height: 14,
    position: 'absolute',
    top: -7,
    zIndex: 1,
    alignSelf: 'center',
  },
  carImgStyle: {
    width: 80,
    height: 50,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  carTypeContainer: {
    paddingVertical: 2,
    paddingHorizontal: 4,
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 3,
    backgroundColor: colors.green,
    borderRadius: 2,
    alignSelf: 'flex-start',
    marginTop: 7,
    paddingVertical: 1,
  },
  ratingText: {
    color: colors.white,
    fontSize: 12,
    lineHeight: 14,
    fontWeight: '900',
    fontFamily: fonts.black,
  },
  starIconStyle: {
    width: 6,
    height: 6,
    marginRight: 2,
  },
  extraInfoStyle: {
    fontSize: 12,
    lineHeight: 14,
    color: colors.textGrey,
    fontStyle: 'italic',
    fontFamily: fonts.regular,
    marginLeft: 3,
    fontWeight: '400',
    marginBottom: 0.5,
    width: 100,
  },

  extraInfoStyleWithCabHeader: {
    fontSize: 12,
    lineHeight: 14,
    color: colors.textGrey,
    fontFamily: fonts.regular,
    fontWeight: '400',
    marginTop: 2,
  },

  facilityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dashView: {
    width: 1,
    height: 10,
    borderRadius: 1,
    backgroundColor: colors.lightSilver,
    marginHorizontal: 8,
  },
  marqueView: {
    flexDirection: 'row',
    alignItems: 'center',
    width: deviceWidth - 45,
  },
  clockIcon: {
    width: 12,
    height: 12,
    marginRight: 8,
  },
  facilityText: {
    fontSize: 12,
    lineHeight: 13,
    fontFamily: fonts.regular,
    fontWeight: '400',
    color: colors.defaultTextColor,
  },
  marqueeTxt: {
    fontSize: 12,
    lineHeight: 16,
    fontFamily: fonts.regular,
    color: colors.black,
  },
  marqueContainer: {
    backgroundColor: colors.creamWhite,
    paddingHorizontal: 13,
    paddingTop: 6,
    paddingBottom: 4,
    marginHorizontal: 16,
  },
  showMoreTxt: {
    fontFamily: fonts.black,
    fontSize: 12,
    marginLeft: 8,
    color: theme.text.primary,
  },
  dashedContainer: {
    height: 0,
    width: '80%',
    marginTop: 12,
    marginBottom: 8,
  },
  dashedBorder: {
    borderColor: colors.lightSilver,
    borderStyle: 'solid',
    borderTopWidth: 1,
    borderRadius: 1,
    height: '100%',
  },
  dashedWrapper: {
    width: '100%',
    height: 2,
    backgroundColor: colors.white,
    marginTop: -2,
  },
  transferzContainer: {
    paddingHorizontal: 9,
    paddingVertical: 2,
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    backgroundColor: '#e8f0fb',
    height: 24,
  },
  transferzBanner: {
    height: 22,
    width: 110,
    position: 'absolute',
    top: -5,
    right: -14,
    paddingVertical: 4,
    borderColor: colors.lightGrey,
    borderWidth: 1,
    borderRadius: 12,
    backgroundColor: colors.grey22,
    paddingHorizontal: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  transferzText: {
    lineHeight: 13,
    fontWeight: '900',
    fontFamily: fonts.regular,
    fontSize: 9,
    marginRight: 2,
    color: colors.textGrey,
    top: -1,
  },
  transferzIcon: {
    height: 6,
    width: 50,
  },
  myBizAssured: {
    width: 92,
    height: 28,
    left: -4,
  },
  infoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 16,
    borderColor: colors.lightSilver,
    borderWidth: 0.5,
    flex: 1,
    overflow: 'hidden',
  },
  cabDetalisExtraInfoIc: {
    width: 20,
    height: 20,
  },
  cabDetalisInfoIc: {
    tintColor: colors.textGrey,
    width: 12,
    height: 12,
    marginTop: 4,
  },
}));

export default styles;
