import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, ScrollView } from 'react-native';
import { FILTER_FLAT_LIST_ITEM_PADDING } from './style';
import FilterTitle from './FilterTitle';
import { getImageUrl } from '../../../Common/Components/HolidayImageUrls';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';
import { widthPixel } from '../../../Styles/holidayNormaliseSize';

const SortingOptions = ({ options, sorterCriterias, onChange }) => {

    const renderOption = (option, index) => {
        const { id, value, name, urlParam } = option;
        let isActive = sorterCriterias?.length && id === sorterCriterias[0].id && sorterCriterias[0].values[0] == value.uniqueId;
        return (
            <TouchableOpacity
                key={name}
                style={[styles.optionContainer, isActive && styles.optionContainerSelected]}
                activeOpacity={0.5}
                onPress={() => onChange(id, name, value.uniqueId, !isActive)}>
                <Image style={styles.icon} source={{ uri: getImageUrl(urlParam) }}/>
                <Text style={[styles.value, isActive && styles.selectedValue]}>{name}</Text>
                <Text style={styles.count}>{value.sorterText}</Text >
            </TouchableOpacity>
        );
    };
    return (
        <View style={styles.container}>
            <FilterTitle title="Sort By" staticText={undefined} />
            <ScrollView style={styles.optionsContainer} horizontal showsHorizontalScrollIndicator={false}>
                {
                    options.map(renderOption)
                }
            </ScrollView>
        </View>
    );
};
export default SortingOptions;

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: FILTER_FLAT_LIST_ITEM_PADDING.horizontal,
        paddingVertical: FILTER_FLAT_LIST_ITEM_PADDING.vertical,
    },
    optionsContainer: {
        ...marginStyles.mt12,
    },
    optionContainer: {
        marginEnd: 8,
        width: widthPixel(84),
        height: 80,
        alignItems: 'center',
        justifyContent: 'center',
        ...holidayBorderRadius.borderRadius8,
        backgroundColor: holidayColors.white,
        borderWidth: 1,
        borderColor: holidayColors.grayBorder,
    },
    optionContainerSelected: {
        backgroundColor: holidayColors.lightBlueBg,
        borderColor: holidayColors.primaryBlue,
    },
    selectedValue: {
        color: holidayColors.primaryBlue,
    },
    value: {
        color: holidayColors.gray,
        ...fontStyles.labelSmallBold,
    },
    count: {
        color: holidayColors.gray,
        ...fontStyles.labelSmallRegular,
    },
    icon: {
        ...marginStyles.mb10,
        width: 18,
        height: 18,
        resizeMode: 'contain',
        tintColor: holidayColors.black,
    },
});
