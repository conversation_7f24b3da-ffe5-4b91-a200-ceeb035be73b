export interface VPPDataProps {
    title: string;
    listConfig: {
        type: string;
    };
    listItems: {
        icon: string;
        text: string;
        subText: string;
    }[];
    footer: {
        ctas: {
            key: string;
            label: string;
            onClick: string;
            type: string;
        }[];
    };
}

export interface VPPStaticDetailsProps {
    addonType: string;
    addonSubType: string;
    trackLocalClickEvent: (name: string, suffix: string, props: { prop1: string, }) => void;
}
export interface InfoCardProps {
    title?: string;
    footer: ctas;
    isBottomSheet: boolean;
    onToggleModalBottomSheet?: (val: boolean) => void;
    trackLocalClickEvent: (name: string, suffix: string, props: { prop1: string, }) => void;
    listConfig: {
        type: string;
    };
    listItems: ListItemProps[];
}

export interface ctas {
    ctas: ctaItemProps[];
}

export interface InfoCardFooterProps {
    ctas: ctaItemProps[];
    isBottomSheet: boolean;
    trackLocalClickEvent: (name: string, suffix: string, props: { prop1: string, }) => void;
    onToggleModalBottomSheet?: (val: boolean) => void;
}

export interface ctaItemProps {
    key: string;
    label: string;
    onClick: string;
    type: string;
    link?: string;
}
export interface listItems {
    listItems: ListItemProps[];
    listType?: string;
    isBottomSheet?: boolean;
}

export interface ListItemProps {
    icon: string;
    subText?: string;
    text: string;
    isSelected?: boolean;
    backGroundColor?: string;
    fontColor?: string;
}
